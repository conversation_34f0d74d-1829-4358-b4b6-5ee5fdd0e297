package org.example.test3;

/**
 * 测试节点布局调整的效果
 * 验证节点坐标和尺寸调整是否能实现直线连接和协调的视觉效果
 */
public class TestLayoutAdjustments {
    
    public static void main(String[] args) {
        System.out.println("🧪 开始测试节点布局和尺寸调整...");
        
        // 测试文件路径
        String inputFile = "src/main/resources/test_input.json";
        String outputFile = "src/main/resources/test_output_adjusted.json";
        
        try {
            // 创建布局处理器
            FlowLayoutProcessor processor = new FlowLayoutProcessor();
            
            // 处理布局
            processor.processFlowLayout(inputFile, outputFile);
            
            System.out.println("\n✅ 布局和尺寸调整测试完成！");
            System.out.println("📄 输出文件: " + outputFile);
            System.out.println("\n📋 调整说明:");
            System.out.println("   1. recall节点的Y坐标已调整为与byStart节点相同");
            System.out.println("   2. 流程开始节点的X坐标已调整为与流程初始化节点相同");
            System.out.println("   3. 流程开始节点的宽度已调整为与流程初始化节点一致");
            System.out.println("   4. 重新提交节点的高度已调整为与byStart节点一致");
            System.out.println("   5. 这些调整将使相关节点间的连线更加直线化，节点尺寸更加协调");
            
            System.out.println("\n🔍 调整详情:");
            System.out.println("   坐标调整:");
            System.out.println("     - recall节点(nodeType=4) Y坐标 = byStart节点 Y坐标");
            System.out.println("     - 流程开始节点(nodeType=1) X坐标 = 流程初始化节点(nodeType=2) X坐标");
            System.out.println("   尺寸调整:");
            System.out.println("     - 流程开始节点(nodeType=1) 宽度 = 流程初始化节点(nodeType=2) 宽度");
            System.out.println("     - 重新提交节点(nodeType=4) 高度 = byStart节点 高度");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
